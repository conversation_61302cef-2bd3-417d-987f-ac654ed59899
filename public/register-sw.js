// Service Worker Registration Script
// This script handles service worker registration with proper error handling

(function() {
  'use strict';

  // Check if service workers are supported
  if (!('serviceWorker' in navigator)) {
    console.log('Service workers are not supported in this browser');
    return;
  }

  // Register service worker when page loads
  window.addEventListener('load', function() {
    registerServiceWorker();
  });

  async function registerServiceWorker() {
    try {
      console.log('Attempting to register service worker...');
      
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      });

      console.log('Service Worker registered successfully:', registration);

      // Handle service worker updates
      registration.addEventListener('updatefound', () => {
        console.log('Service Worker update found');
        const newWorker = registration.installing;
        
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('New service worker installed, prompting for update');
              // You could show a notification to the user here
            }
          });
        }
      });

      // Send initial authentication state
      sendInitialAuthState(registration);

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);

    } catch (error) {
      console.warn('Service Worker registration failed:', error);
      // Don't throw error - app should work without SW
    }
  }

  function sendInitialAuthState(registration) {
    try {
      // Check if user is authenticated by looking for session cookies
      const isLoggedIn = document.cookie.includes('next-auth.session-token') ||
                        document.cookie.includes('__Secure-next-auth.session-token') ||
                        localStorage.getItem('next-auth.session-token');

      if (registration.active) {
        registration.active.postMessage({
          type: 'AUTH_STATE_CHANGE',
          isAuthenticated: Boolean(isLoggedIn)
        });
        console.log('Initial auth state sent to SW:', Boolean(isLoggedIn));
      }
    } catch (error) {
      console.warn('Failed to send initial auth state:', error);
    }
  }

  function handleServiceWorkerMessage(event) {
    console.log('Message from service worker:', event.data);
    
    if (event.data && event.data.type === 'CACHE_UPDATED') {
      console.log('Cache updated by service worker');
    }
  }

  // Export functions for use by the app
  window.swUtils = {
    updateAuthState: function(isAuthenticated) {
      if (navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'AUTH_STATE_CHANGE',
          isAuthenticated: isAuthenticated
        });
      }
    },
    
    clearAuthCaches: function() {
      if (navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'CLEAR_AUTH_CACHES'
        });
      }
    }
  };

})();
