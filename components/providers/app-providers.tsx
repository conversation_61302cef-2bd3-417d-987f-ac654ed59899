'use client';

import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'next-themes';
import { ReactNode, useEffect, useState } from 'react';

interface AppProvidersProps {
  children: ReactNode;
}

// Separate theme provider to isolate hydration issues
function ClientThemeProvider({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      storageKey="theme"
      enableColorScheme={false}
    >
      {children}
    </ThemeProvider>
  );
}

// Separate session provider to isolate hydration issues
function ClientSessionProvider({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <SessionProvider
      refetchInterval={5 * 60}
      refetchOnWindowFocus={true}
      basePath="/api/auth"
    >
      {children}
    </SessionProvider>
  );
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <ClientSessionProvider>
      <ClientThemeProvider>
        {children}
      </ClientThemeProvider>
    </ClientSessionProvider>
  );
}
