import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Helper functions for common database operations
export async function createUser(data: {
  email: string;
  name: string;
  password?: string;
  role?: string;
}) {
  return prisma.user.create({
    data: {
      email: data.email,
      name: data.name,
      role: data.role || 'user',
    },
  });
}

export async function getUserByEmail(email: string) {
  return prisma.user.findUnique({
    where: { email },
    include: {
      savedSearches: true,
      properties: true,
    },
  });
}

export async function getUserById(id: string) {
  return prisma.user.findUnique({
    where: { id },
    include: {
      savedSearches: true,
      properties: true,
    },
  });
}

export async function createProperty(data: {
  address: string;
  userId: string;
  parcelId?: string;
  latitude?: number;
  longitude?: number;
  lotSize?: number;
  lotDimensions?: any;
  zoning?: string;
  qctStatus?: boolean;
  ddaStatus?: boolean;
  neighborhoodChangeZone?: boolean;
  heightRestriction?: number;
  densityPotential?: number;
  frontages?: number;
  gradeChange?: number;
  historicalDesignation?: boolean;
  environmentalIssues?: boolean;
  transitAccess?: boolean;
  transitDistance?: number;
  evaluationScore?: number;
  evaluationStatus?: string;
  rawData?: any;
}) {
  return prisma.property.create({
    data,
  });
}

export async function getPropertiesByUser(userId: string) {
  return prisma.property.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    include: {
      reports: true,
    },
  });
}

export async function createSavedSearch(data: {
  name: string;
  criteria: any;
  userId: string;
  results?: any;
}) {
  return prisma.savedSearch.create({
    data,
  });
}

export async function getSavedSearchesByUser(userId: string) {
  return prisma.savedSearch.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
  });
}

export async function createReport(data: {
  propertyId: string;
  userId: string;
  reportType: string;
  content: any;
  pdfUrl?: string;
  shareableUrl?: string;
}) {
  return prisma.report.create({
    data,
  });
}

export async function getReportsByProperty(propertyId: string) {
  return prisma.report.findMany({
    where: { propertyId },
    orderBy: { createdAt: 'desc' },
  });
}

export async function createScrapingJob(data: {
  propertyId?: string;
  jobType: string;
  status?: string;
  progress?: number;
  result?: any;
  error?: string;
}) {
  return prisma.scrapingJob.create({
    data: {
      ...data,
      status: data.status || 'pending',
      progress: data.progress || 0,
    },
  });
}

export async function updateScrapingJob(id: string, data: {
  status?: string;
  progress?: number;
  result?: any;
  error?: string;
  startedAt?: Date;
  completedAt?: Date;
}) {
  return prisma.scrapingJob.update({
    where: { id },
    data,
  });
}

export async function getCachedData(key: string) {
  const cached = await prisma.cachedData.findUnique({
    where: { key },
  });

  if (!cached || cached.expiresAt < new Date()) {
    return null;
  }

  return cached.data;
}

export async function setCachedData(key: string, data: any, expiresInMinutes: number = 60) {
  const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);

  return prisma.cachedData.upsert({
    where: { key },
    update: { data, expiresAt },
    create: { key, data, expiresAt },
  });
}

export async function clearExpiredCache() {
  return prisma.cachedData.deleteMany({
    where: {
      expiresAt: {
        lt: new Date(),
      },
    },
  });
}
