"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  MapPin,
  Plus,
  Eye,
  FileText,
  MoreHorizontal,
  SortAsc,
  SortDesc,
} from "lucide-react";

// Mock data for demonstration
const mockProperties = [
  {
    id: "1",
    address: "123 Main St, Seattle, WA 98101",
    parcelId: "PAR-001-2024",
    lotSize: 5000,
    zoning: "R-1",
    evaluationScore: 8.5,
    status: "Under Review",
    addedDate: "2024-01-15",
    qctStatus: true,
    ddaStatus: false,
    transitDistance: 0.3,
    price: 450000,
  },
  {
    id: "2",
    address: "456 Oak Ave, Portland, OR 97201",
    parcelId: "PAR-002-2024",
    lotSize: 7500,
    zoning: "R-2",
    evaluationScore: 7.2,
    status: "Approved",
    addedDate: "2024-01-14",
    qctStatus: true,
    ddaStatus: true,
    transitDistance: 0.1,
    price: 680000,
  },
  {
    id: "3",
    address: "789 Pine Rd, Vancouver, WA 98660",
    parcelId: "PAR-003-2024",
    lotSize: 4200,
    zoning: "R-1",
    evaluationScore: 6.8,
    status: "Rejected",
    addedDate: "2024-01-13",
    qctStatus: false,
    ddaStatus: false,
    transitDistance: 1.2,
    price: 320000,
  },
  {
    id: "4",
    address: "321 Cedar Blvd, Bellevue, WA 98004",
    parcelId: "PAR-004-2024",
    lotSize: 8000,
    zoning: "R-3",
    evaluationScore: 9.1,
    status: "Approved",
    addedDate: "2024-01-12",
    qctStatus: true,
    ddaStatus: true,
    transitDistance: 0.2,
    price: 850000,
  },
];

export default function PropertiesPage() {
  const [properties, setProperties] = useState(mockProperties);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [zoningFilter, setZoningFilter] = useState("all");
  const [sortBy, setSortBy] = useState("addedDate");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [isLoading, setIsLoading] = useState(false);

  // Filter and sort properties
  const filteredProperties = properties
    .filter((property) => {
      const matchesSearch = property.address
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === "all" || property.status === statusFilter;
      const matchesZoning =
        zoningFilter === "all" || property.zoning === zoningFilter;
      return matchesSearch && matchesStatus && matchesZoning;
    })
    .sort((a, b) => {
      const aValue = a[sortBy as keyof typeof a];
      const bValue = b[sortBy as keyof typeof b];
      
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortOrder === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
      }
      
      return 0;
    });

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Approved":
        return "default";
      case "Rejected":
        return "destructive";
      case "Under Review":
        return "secondary";
      default:
        return "outline";
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Properties
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage and analyze your property portfolio
          </p>
        </div>
        <div className="flex space-x-2 mt-4 sm:mt-0">
          <Button variant="outline" asChild>
            <Link href="/properties/search">
              <Search className="h-4 w-4 mr-2" />
              Search Properties
            </Link>
          </Button>
          <Button asChild>
            <Link href="/properties/add">
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Filters & Search</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by address..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Under Review">Under Review</SelectItem>
                  <SelectItem value="Approved">Approved</SelectItem>
                  <SelectItem value="Rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Zoning</label>
              <Select value={zoningFilter} onValueChange={setZoningFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All zoning" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Zoning</SelectItem>
                  <SelectItem value="R-1">R-1</SelectItem>
                  <SelectItem value="R-2">R-2</SelectItem>
                  <SelectItem value="R-3">R-3</SelectItem>
                  <SelectItem value="Commercial">Commercial</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Sort By</label>
              <div className="flex space-x-2">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="addedDate">Date Added</SelectItem>
                    <SelectItem value="evaluationScore">Score</SelectItem>
                    <SelectItem value="price">Price</SelectItem>
                    <SelectItem value="lotSize">Lot Size</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                  }
                >
                  {sortOrder === "asc" ? (
                    <SortAsc className="h-4 w-4" />
                  ) : (
                    <SortDesc className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between mb-6">
        <p className="text-gray-600 dark:text-gray-400">
          Showing {filteredProperties.length} of {properties.length} properties
        </p>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Properties Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredProperties.map((property) => (
          <Card key={property.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg line-clamp-2">
                    {property.address}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {property.parcelId}
                  </CardDescription>
                </div>
                <Badge variant={getStatusBadgeVariant(property.status)}>
                  {property.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Lot Size:</span>
                    <div className="font-medium">{property.lotSize.toLocaleString()} sqft</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Zoning:</span>
                    <div className="font-medium">{property.zoning}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Score:</span>
                    <div className="font-medium">{property.evaluationScore}/10</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Price:</span>
                    <div className="font-medium">{formatPrice(property.price)}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <div className="flex items-center">
                    <div
                      className={`w-2 h-2 rounded-full mr-1 ${
                        property.qctStatus ? "bg-green-500" : "bg-red-500"
                      }`}
                    />
                    QCT
                  </div>
                  <div className="flex items-center">
                    <div
                      className={`w-2 h-2 rounded-full mr-1 ${
                        property.ddaStatus ? "bg-green-500" : "bg-red-500"
                      }`}
                    />
                    DDA
                  </div>
                  <div>Transit: {property.transitDistance}mi</div>
                </div>

                <div className="flex items-center justify-between pt-2">
                  <span className="text-xs text-gray-500">
                    Added {formatDate(property.addedDate)}
                  </span>
                  <div className="flex space-x-1">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/properties/${property.id}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/properties/${property.id}/report`}>
                        <FileText className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredProperties.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No properties found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try adjusting your search criteria or add a new property.
            </p>
            <Button asChild>
              <Link href="/properties/add">
                <Plus className="h-4 w-4 mr-2" />
                Add Property
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
